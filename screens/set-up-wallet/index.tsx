import { View } from 'react-native';
import { CustomButton } from '@/components/Button';
import { useCreateWallet } from '@/hooks/useCreateWallet';
import { useGetRecoveryKey } from '@/hooks/useGetRecoveryKey';
import { useSetupWallet } from '@/hooks/useSetupWallet';

type Props = {};

export const SetupWallet = (props: Props) => {
  const { data: recoveryKey } = useGetRecoveryKey();
  const { mutateAsync: createWallet, isPending: isCreatingWallet } = useCreateWallet();
  const { mutateAsync: setupWallet, isPending: isSettingUpWallet } = useSetupWallet();

  const handleCreateWallet = async () => {
    try {
      if (!recoveryKey) return;

      const wallet = await createWallet(recoveryKey);
      if (!wallet) return;

      const { privateKey, seedPhrase } = wallet;

      await setupWallet({ privateKey, seedPhrase, recoveryKey });
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <View>
      <CustomButton type='primary' onPress={handleCreateWallet} disabled={isCreatingWallet || isSettingUpWallet}>
        Create Wallet
      </CustomButton>
    </View>
  );
};
