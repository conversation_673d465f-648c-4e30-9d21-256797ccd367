import { router } from 'expo-router';
import { ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { CustomButton } from '@/components/Button';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { useGetBiometric } from '@/hooks/useGetBiometric';
import { useGetPin } from '@/hooks/useGetPin';
import { useGetRecoveryKey } from '@/hooks/useGetRecoveryKey';
import { useTheme } from '@/hooks/useThemeColor';
import { StepAccountCreation } from './components/StepAccountCreation';
import { StepWalletCreation } from './components/StepWalletCreation';

type Props = {};

export const SetUpSecurity = (props: Props) => {
  const { styles } = useStyles();

  const { data: hashingPassCode } = useGetPin();
  const { data: biometricEnabled } = useGetBiometric();
  const { data: recoveryKey } = useGetRecoveryKey();

  const isStep1Complete = !!hashingPassCode && biometricEnabled && !!recoveryKey;

  const handleConfirm = () => {
    if (!isStep1Complete) {
      return router.navigate('/set-up-pin');
    }

    router.navigate('/set-up-wallet');
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView bounces={false} style={styles.pH} showsVerticalScrollIndicator={false}>
        <Show when={isStep1Complete}>
          <ThemedText type='tinyLight' style={styles.nextStepTitle}>
            Next Step
          </ThemedText>
        </Show>

        <Spacer height={8} />

        <ThemedText type='title' style={styles.title}>
          {isStep1Complete ? 'Set up wallet' : 'Set Up Security'}
        </ThemedText>

        <Spacer height={8} />

        <ThemedText type='smallLight' style={styles.description}>
          {isStep1Complete
            ? 'Start by creating a highly secure Multi Sig wallet to manage your funds and prevent unauthorized payments'
            : 'Take the first step to protect your wallet. Secure you wallet on this device so only you can access'}
        </ThemedText>

        <Spacer height={32} />

        <StepAccountCreation />

        <StepWalletCreation />
      </ScrollView>

      <View style={styles.pH}>
        <CustomButton type='primary' onPress={handleConfirm}>
          {isStep1Complete ? 'Setup wallet' : 'Get started'}
        </CustomButton>
      </View>
    </SafeAreaView>
  );
};

const useStyles = () => {
  const backgroundColor = useTheme('background');
  const white90 = useTheme('white90');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor,
      paddingVertical: 16,
    },
    title: {
      // color: primary,
      textAlign: 'center',
    },
    description: {
      color: white90,
      textAlign: 'center',
    },
    nextStepTitle: {
      color: '#B7B7C7',
      textAlign: 'center',
    },
    pH: {
      paddingHorizontal: 16,
    },
  });

  return { styles };
};
