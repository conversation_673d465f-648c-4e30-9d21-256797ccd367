import { CommonActions } from '@react-navigation/native';
import * as Clipboard from 'expo-clipboard';
import { router, useNavigation } from 'expo-router';
import { useEffect, useState } from 'react';
import { ScrollView, StyleSheet, Switch, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Icons } from '@/assets/icons';
import { CustomButton } from '@/components/Button';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { useGetRecoveryKey } from '@/hooks/useGetRecoveryKey';
import { useSetupRecoveryKey } from '@/hooks/useSetupRecoveryKey';
import { useTheme } from '@/hooks/useThemeColor';

type Props = {};

export const SetUpRecoveryKey = (props: Props) => {
  const { styles } = useStyles();
  const navigation = useNavigation();

  const [isConfirmed, setIsConfirmed] = useState(false);

  const { data: recoveryKey } = useGetRecoveryKey();
  const { mutateAsync: setupRecoveryKey } = useSetupRecoveryKey();

  const handleMaybeLater = () => {
    navigation.dispatch(CommonActions.reset({ index: 0, routes: [{ name: 'set-up-security' }] }));
  };

  const handleNextStep = async () => {
    if (!recoveryKey) return;
    router.navigate('/account-creation-success');
  };

  const toggleConfirm = () => {
    setIsConfirmed(!isConfirmed);
  };

  const handleCopy = async () => {
    if (!recoveryKey) return;

    await Clipboard.setStringAsync(recoveryKey);
  };

  useEffect(() => {
    if (!recoveryKey) {
      setupRecoveryKey();
    }
  }, [recoveryKey, setupRecoveryKey]);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView bounces={false} showsVerticalScrollIndicator={false}>
        <ThemedText type='title'>Recovery key</ThemedText>

        <Spacer height={16} />

        <ThemedText type='smallLight'>Copy and securely store the following recovery code</ThemedText>

        <Spacer height={16} />

        <View style={styles.containerKey}>
          <View style={styles.boxKey}>
            <ThemedText type='tinyLight' style={styles.keyTitle}>
              Recovery key
            </ThemedText>

            <ThemedText>{recoveryKey}</ThemedText>
          </View>

          <CustomButton type='secondary' onPress={handleCopy}>
            <Icons.Copy size={20} color='#fff' />
          </CustomButton>
        </View>

        <Spacer height={16} />

        <ThemedText type='small'>
          This code will allow you to regain access to your GeoSafe account if you lose access or change device
        </ThemedText>

        <Spacer height={16} />

        <View style={styles.switchContainer}>
          <Switch value={isConfirmed} onValueChange={toggleConfirm} />

          <ThemedText onPress={toggleConfirm} type='small'>
            Confirm I have secured the recovery key
          </ThemedText>
        </View>
      </ScrollView>

      <Spacer height={16} />

      <View style={styles.actions}>
        <CustomButton type='primary' onPress={handleNextStep} disabled={!isConfirmed || !recoveryKey}>
          Continue
        </CustomButton>

        <CustomButton type='secondary' onPress={handleMaybeLater}>
          Cancel
        </CustomButton>
      </View>
    </SafeAreaView>
  );
};

const useStyles = () => {
  const white05 = useTheme('white05');
  const white15 = useTheme('white15');
  const white35 = useTheme('white35');

  const styles = StyleSheet.create({
    switchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
    },
    container: {
      flex: 1,
      padding: 16,
    },
    actions: {
      flexDirection: 'column',
      gap: 8,
    },
    containerKey: {
      backgroundColor: white05,
      borderWidth: 1,
      borderColor: white15,
      borderRadius: 16,
      padding: 16,
      gap: 16,
      flexDirection: 'row',
      alignItems: 'center',
    },
    boxKey: {
      flex: 1,
      flexDirection: 'column',
      gap: 2,
    },
    keyTitle: {
      color: white35,
    },
  });

  return { styles };
};
