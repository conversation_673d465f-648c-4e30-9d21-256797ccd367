import React, { useState } from 'react';

import {
  TextInput as RNTextInput,
  StyleProp,
  StyleSheet,
  TextInputProps,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import { FontWeight } from '@/constants/Colors';
import { useTheme } from '@/hooks/useThemeColor';
import { ThemedText } from '../ThemedText';

export interface CustomTextInputProps extends Omit<TextInputProps, 'style'> {
  error?: string | boolean;
  label?: string;
  helperText?: string;
  isPassword?: boolean;
  inputStyle?: StyleProp<TextStyle>;
  containerStyle?: StyleProp<ViewStyle>;
  rightIcon?: React.ReactNode;
  leftIcon?: React.ReactNode;
  leftIconStyle?: StyleProp<ViewStyle>;
  onRightIconPress?: () => void;
  onLeftIconPress?: () => void;
}

export const TextInput = React.forwardRef<RNTextInput, CustomTextInputProps>(
  (
    {
      error,
      containerStyle,
      label,
      helperText,
      isPassword,
      secureTextEntry,
      inputStyle,
      rightIcon,
      leftIcon,
      leftIconStyle,
      onRightIconPress,
      onLeftIconPress,
      onFocus,
      onBlur,
      editable,
      ...props
    },
    ref
  ) => {
    const hasError = !!error;
    const [passwordVisible, setPasswordVisible] = useState(false);
    const [isFocused, setIsFocused] = useState(false);
    const { styles } = useStyles();

    const _togglePasswordVisibility = () => {
      setPasswordVisible(!passwordVisible);
    };

    const handleFocus = (e: any) => {
      setIsFocused(true);
      if (onFocus) onFocus(e);
    };

    const handleBlur = (e: any) => {
      setIsFocused(false);
      if (onBlur) onBlur(e);
    };

    const isPasswordField = isPassword;
    const isSecureTextEntry = secureTextEntry;

    return (
      <View style={[styles.container, containerStyle]}>
        {label && (
          <ThemedText style={[styles.label]} type='small'>
            {label}
          </ThemedText>
        )}

        <View style={styles.inputContainer}>
          {leftIcon && (
            <TouchableOpacity
              style={[styles.leftIconButton, leftIconStyle]}
              onPress={onLeftIconPress}
              activeOpacity={onLeftIconPress ? 0.7 : 1}
              disabled={!onLeftIconPress}
            >
              {leftIcon}
            </TouchableOpacity>
          )}

          <RNTextInput
            ref={ref}
            placeholderTextColor='rgba(255, 255, 255, 0.4)'
            style={[
              styles.input,
              isFocused ? styles.inputFocused : null,
              rightIcon || isPasswordField ? styles.inputWithRightIcon : null,
              leftIcon ? styles.inputWithLeftIcon : null,
              hasError ? styles.inputError : null,
              editable === false ? styles.inputContainerDisabled : null,
              inputStyle,
            ]}
            secureTextEntry={isPasswordField || isSecureTextEntry ? !passwordVisible : false}
            onFocus={handleFocus}
            onBlur={handleBlur}
            editable={editable}
            {...props}
          />

          {rightIcon && !isPasswordField && (
            <TouchableOpacity
              style={styles.rightIconButton}
              onPress={onRightIconPress}
              activeOpacity={onRightIconPress ? 0.7 : 1}
              disabled={!onRightIconPress}
            >
              {rightIcon}
            </TouchableOpacity>
          )}
        </View>

        {hasError && typeof error === 'string' && (
          <ThemedText type='tinyLight' style={styles.errorText}>
            {error}
          </ThemedText>
        )}

        {!hasError && helperText && <ThemedText style={styles.helperText}>{helperText}</ThemedText>}
      </View>
    );
  }
);

const useStyles = () => {
  const primary = useTheme('primary');

  const styles = StyleSheet.create({
    container: {
      width: '100%',
    },
    inputContainer: {
      position: 'relative',
      width: '100%',
      flexDirection: 'row',
      alignItems: 'center',
    },
    inputContainerDisabled: {
      opacity: 0.6,
    },
    label: {
      color: '#fff',
      fontSize: 16,
      marginBottom: 16,
      ...FontWeight.medium,
    },
    errorLabel: {
      color: 'red',
    },
    input: {
      fontSize: 16,
      ...FontWeight.regular,
      backgroundColor: '#1F1E1E',
      borderRadius: 16,
      height: 48,
      paddingHorizontal: 16,
      color: '#fff',
      borderWidth: 1,
      borderColor: 'transparent',
      flex: 1,
    },
    inputFocused: {
      borderColor: primary,
    },
    inputWithRightIcon: {
      paddingRight: 50, // Make space for the right icon
    },
    inputWithLeftIcon: {
      paddingLeft: 45, // Make space for the left icon
    },
    inputError: {
      borderColor: 'red',
    },
    errorText: {
      color: 'red',
      marginTop: 6,
      textAlign: 'right',
    },
    helperText: {
      color: '#fff',
      fontSize: 12,
      marginTop: 4,
    },
    rightIconButton: {
      position: 'absolute',
      right: 16,
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1,
    },
    leftIconButton: {
      position: 'absolute',
      left: 16,
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1,
    },
  });

  return { styles };
};

export default TextInput;
