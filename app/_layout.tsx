import { Poppins_300Light } from '@expo-google-fonts/poppins/300Light';
import { Poppins_400Regular } from '@expo-google-fonts/poppins/400Regular';
import { Poppins_500Medium } from '@expo-google-fonts/poppins/500Medium';
import { Poppins_600SemiBold } from '@expo-google-fonts/poppins/600SemiBold';
import { Poppins_700Bold } from '@expo-google-fonts/poppins/700Bold';
import { useFonts } from '@expo-google-fonts/poppins/useFonts';
import { router, Stack, useNavigation } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useCallback, useEffect, useRef, useState } from 'react';
import { AppState, AppStateStatus, StyleSheet, TouchableWithoutFeedback, View } from 'react-native';
import { install } from 'react-native-quick-crypto';
import { CircleProgress } from '@/components/ui/CircleProgress';
import { useTheme } from '@/hooks/useThemeColor';
import Providers from '@/utils/providers';

import 'react-native-reanimated';
import { useGetPin } from '@/hooks/useGetPin';

install();

// 5 minutes in milliseconds
const INACTIVITY_TIMEOUT = 5 * 60 * 1000;
// Check every 30 seconds
const CHECK_INTERVAL = 30 * 1000;

function LayoutWrapper() {
  const { styles } = useStyles();
  const [appState, setAppState] = useState<AppStateStatus>();
  const lastActiveTime = useRef(Date.now());
  const timeoutRef = useRef<number | undefined>(undefined);

  const navigation = useNavigation();

  const { data: hashingPassCode } = useGetPin();

  const resetInactivityTimer = useCallback(() => {
    lastActiveTime.current = Date.now();
  }, []);

  const checkInactivity = useCallback(() => {
    const timeSinceLastActivity = Date.now() - lastActiveTime.current;
    if (timeSinceLastActivity >= INACTIVITY_TIMEOUT) {
      router.navigate('/auth');
    }
  }, []);

  useEffect(() => {
    const unsubscribe = navigation.addListener('state', () => {
      resetInactivityTimer();
    });

    return unsubscribe;
  }, [navigation, resetInactivityTimer]);

  useEffect(() => {
    if (!appState && !!hashingPassCode) {
      router.navigate('/auth');
    }
    const appStateSubscription = AppState.addEventListener('change', (nextAppState) => {
      setAppState(nextAppState);
      if (nextAppState === 'active') {
        resetInactivityTimer();
      }
    });

    // Start inactivity check timer
    timeoutRef.current = setInterval(checkInactivity, CHECK_INTERVAL);

    return () => {
      appStateSubscription.remove();
      if (timeoutRef.current) {
        clearInterval(timeoutRef.current);
      }
    };
  }, [appState, hashingPassCode, checkInactivity, resetInactivityTimer]);

  return (
    <TouchableWithoutFeedback onPress={resetInactivityTimer}>
      <View style={styles.container}>
        <Stack
          screenOptions={{
            headerStyle: styles.headerStyle,
            contentStyle: styles.container,
            headerTitleAlign: 'center',
            headerTintColor: '#fff',
            headerBackButtonDisplayMode: 'minimal',
          }}
          initialRouteName='(app)'
        >
          <Stack.Screen name='(app)' options={{ headerShown: false }} />

          <Stack.Screen name='onboarding' options={{ headerShown: false }} />

          <Stack.Screen name='set-up-security' options={{ title: '', headerShown: false }} />

          <Stack.Screen
            name='set-up-pin'
            options={{
              title: 'Account creation',
              headerRight: () => <CircleProgress radius={22} borderWidth={1.5} percentage={100 / 3} innerText='1/3' />,
            }}
          />

          <Stack.Screen
            name='set-up-biometric'
            options={{
              title: 'Account creation',
              headerRight: () => <CircleProgress radius={22} borderWidth={1.5} percentage={200 / 3} innerText='2/3' />,
            }}
          />

          <Stack.Screen
            name='set-up-notification'
            options={{
              title: 'Account creation',
              headerRight: () => <CircleProgress radius={22} borderWidth={1.5} percentage={200 / 3} innerText='2/3' />,
            }}
          />

          <Stack.Screen
            name='set-up-location'
            options={{
              title: 'Account creation',
              headerRight: () => <CircleProgress radius={22} borderWidth={1.5} percentage={200 / 3} innerText='2/3' />,
            }}
          />

          <Stack.Screen
            name='set-up-recovery-key'
            options={{
              title: 'Account creation',
              headerRight: () => <CircleProgress radius={22} borderWidth={1.5} percentage={100} innerText='3/3' />,
            }}
          />

          <Stack.Screen name='account-creation-success' options={{ headerShown: false }} />

          <Stack.Screen name='auth' options={{ headerShown: false, gestureEnabled: false }} />

          <Stack.Screen name='+not-found' />
        </Stack>

        <StatusBar style='light' />
      </View>
    </TouchableWithoutFeedback>
  );
}

export default function RootLayout() {
  const [fontsLoaded] = useFonts({
    // Poppins_100Thin,
    // Poppins_100Thin_Italic,
    // Poppins_200ExtraLight,
    // Poppins_200ExtraLight_Italic,
    Poppins_300Light,
    // Poppins_300Light_Italic,
    Poppins_400Regular,
    // Poppins_400Regular_Italic,
    Poppins_500Medium,
    // Poppins_500Medium_Italic,
    Poppins_600SemiBold,
    // Poppins_600SemiBold_Italic,
    Poppins_700Bold,
    // Poppins_700Bold_Italic,
    // Poppins_800ExtraBold,
    // Poppins_800ExtraBold_Italic,
    // Poppins_900Black,
    // Poppins_900Black_Italic
  });

  if (!fontsLoaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <Providers>
      <LayoutWrapper />
    </Providers>
  );
}

const useStyles = () => {
  const backgroundColor = useTheme('background');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: backgroundColor,
    },
    headerStyle: {
      backgroundColor: backgroundColor,
    },
  });

  return { styles };
};
