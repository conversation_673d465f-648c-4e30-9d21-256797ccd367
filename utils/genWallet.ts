import { HDKey } from '@scure/bip32';
import { mnemonicToSeedSync } from '@scure/bip39';
import GeoHash from 'ngeohash';
import QuickCrypto from 'react-native-quick-crypto';
import WordLists from './en.json';

type Props = {
  locationHash: string;
  recoveryKey: string;
  iterations?: number;
  keylen?: number;
};

export const genEOAWallet = async ({ locationHash, recoveryKey, iterations = 1000, keylen = 32 }: Props) => {
  try {
    const inputLK = concatData([locationHash, recoveryKey]);
    const lkHashInitial = QuickCrypto.createHash('sha256').update(inputLK).digest('hex');

    const salt = Buffer.from(recoveryKey, 'hex');
    const derived = QuickCrypto.pbkdf2Sync(lkHashInitial, salt, iterations, keylen, 'sha256').toString('hex');

    const seedPhrase = entropyToMnemonic(derived, WordLists);

    return {
      seedPhrase,
    };
  } catch (error) {
    console.error(error);
    return null;
  }
};

export function geoHash(latitude: number, longitude: number, precision = 7) {
  return GeoHash.encode(latitude, longitude, precision);
}

export function mnemonicToPK(seedPhrase: string) {
  const seed = mnemonicToSeedSync(seedPhrase);
  const hdkey = HDKey.fromMasterSeed(seed);
  const path = "m/44'/60'/0'/0/0";
  const childKey = hdkey.derive(path);
  const privateKeyHex = Buffer.from(childKey.privateKey!).toString('hex');

  return privateKeyHex;
}

export function encodeWallet(recoveryKey: string, privateKey: string, seedPhrase: string) {
  const iv = Buffer.from(recoveryKey, 'hex');

  // Encode privateKey
  const privateKeyCipher = QuickCrypto.createCipheriv('aes-256-cbc', recoveryKey, iv);
  const privateKeyEncryptedPart1 = privateKeyCipher.update(privateKey) as Buffer;
  const privateKeyEncryptedPart2 = privateKeyCipher.final() as Buffer;
  const privateKeyEncrypted = Buffer.concat([privateKeyEncryptedPart1, privateKeyEncryptedPart2]);
  const privateKeyEncryptedHex = privateKeyEncrypted.toString('hex');

  // Encode seed phrase
  const seedPhraseCipher = QuickCrypto.createCipheriv('aes-256-cbc', recoveryKey, iv);
  const seedPhraseEncryptedPart1 = seedPhraseCipher.update(seedPhrase) as Buffer;
  const seedPhraseEncryptedPart2 = seedPhraseCipher.final() as Buffer;
  const seedPhraseEncrypted = Buffer.concat([seedPhraseEncryptedPart1, seedPhraseEncryptedPart2]);
  const seedPhraseEncryptedHex = seedPhraseEncrypted.toString('hex');

  return {
    privateKeyHash: privateKeyEncryptedHex,
    seedPhraseHash: seedPhraseEncryptedHex,
  };
}

export function decodeWallet(recoveryKey: string, privateKeyHash: string, seedPhraseHash: string) {
  const iv = Buffer.from(recoveryKey, 'hex');

  // Decode privateKey
  const privateKeyDecipher = QuickCrypto.createDecipheriv('aes-256-cbc', recoveryKey, iv);
  const privateKeyDecryptedPart1 = privateKeyDecipher.update(Buffer.from(privateKeyHash, 'hex')) as Buffer;
  const privateKeyDecryptedPart2 = privateKeyDecipher.final() as Buffer;
  const privateKeyDecrypted = Buffer.concat([privateKeyDecryptedPart1, privateKeyDecryptedPart2]);
  const privateKeyDecryptedHex = privateKeyDecrypted.toString('hex');

  // Decode seed phrase
  const seedPhraseDecipher = QuickCrypto.createDecipheriv('aes-256-cbc', recoveryKey, iv);
  const seedPhraseDecryptedPart1 = seedPhraseDecipher.update(Buffer.from(seedPhraseHash, 'hex')) as Buffer;
  const seedPhraseDecryptedPart2 = seedPhraseDecipher.final() as Buffer;
  const seedPhraseDecrypted = Buffer.concat([seedPhraseDecryptedPart1, seedPhraseDecryptedPart2]);
  const seedPhraseDecryptedHex = seedPhraseDecrypted.toString();

  return {
    privateKey: privateKeyDecryptedHex,
    seedPhrase: seedPhraseDecryptedHex,
  };
}

function concatData(data: string[]): string {
  return data.join('');
}

function checksumBits(entropyBuffer: ArrayBuffer, entropyBufferLength: number) {
  const hash = QuickCrypto.createHash('sha256').update(entropyBuffer).digest();

  // Calculated constants from BIP39
  const ENT = entropyBufferLength * 8;
  const CS = ENT / 32;

  return bytesToBinary([].slice.call(hash)).slice(0, CS);
}

function bytesToBinary(bytes: number[]) {
  return bytes
    .map(function (x) {
      return lpad(x.toString(2), '0', 8);
    })
    .join('');
}

function lpad(str: string, padString: string, length: number): string {
  while (str.length < length) str = padString + str;
  return str;
}

function entropyToMnemonic(entropy: string, wordlist: string[]): string {
  const entropyBuffer = Buffer.from(entropy, 'hex');
  const entropyBits = bytesToBinary([].slice.call(entropyBuffer));
  const checksum = checksumBits(entropyBuffer.buffer, entropyBuffer.length);

  const bits = entropyBits + checksum;
  const chunks = bits.match(/(.{1,11})/g);

  if (!chunks) throw 'no chunks';

  const words = chunks.map((binary: any) => {
    const index = Number.parseInt(binary, 2);
    return wordlist[index];
  });

  return words.join(' ');
}
