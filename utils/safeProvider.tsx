import Safe, { PredictedSafeProps } from '@safe-global/protocol-kit';
import { PropsWithChildren, useEffect } from 'react';
import { sepolia } from 'viem/chains';
import { useGetWallet } from '@/hooks/useGetWallet';

type Props = {};

export const SafeProvider = ({ children }: PropsWithChildren<Props>) => {
  const { data: wallet, isPending } = useGetWallet();

  useEffect(() => {
    if (!wallet?.privateKey) return;
    const SIGNER_PRIVATE_KEY = wallet?.privateKey;

    const predictedSafe: PredictedSafeProps = {
      safeAccountConfig: {
        owners: [SIGNER_PRIVATE_KEY],
        threshold: 1,
      },
    };

    (async () => {
      const protocolKit = await Safe.init({
        provider: sepolia.rpcUrls.default.http[0],
        signer: SIGNER_PRIVATE_KEY,
        predictedSafe,
      });

      const isDeployed = await protocolKit.isSafeDeployed();
      console.log({ isDeployed });
    })();
  }, [wallet]);

  if (isPending) return null;

  return <>{children}</>;
};
