import { useMutation } from '@tanstack/react-query';
import { genEOAWallet, geoHash, mnemonicToPK } from '@/utils/genWallet';
import { useGetLocation } from './useGetLocation';

export const useCreateWallet = () => {
  const { mutateAsync: getLocation } = useGetLocation();

  return useMutation({
    mutationFn: async (recoveryKey: string) => {
      const location = await getLocation();

      if (!location) throw new Error('Location not found');

      const locationHash = geoHash(location?.coords.latitude, location?.coords.longitude, 7);

      const wallet = await genEOAWallet({
        locationHash,
        recoveryKey,
      });

      if (!wallet?.seedPhrase) return null;

      const privateKeyHex = mnemonicToPK(wallet.seedPhrase);

      return {
        privateKey: privateKeyHex,
        seedPhrase: wallet?.seedPhrase,
      };
    },
  });
};
